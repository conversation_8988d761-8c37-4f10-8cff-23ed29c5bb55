@echo off
chcp 65001 >nul
echo 编译简化版 OPC UA 客户端程序
echo ============================

echo.
echo 编译简化版客户端 (opcua_simple.exe)...
g++ -DUSE_OPEN62541 -std=c++11 -I"C:\vcpkg\installed\x64-windows\include" -L"C:\vcpkg\installed\x64-windows\lib" opcua_simple.cpp -lopen62541 -lws2_32 -o opcua_simple.exe

if %ERRORLEVEL% EQU 0 (
    echo ✓ opcua_simple.exe 编译成功
) else (
    echo ✗ opcua_simple.exe 编译失败
    goto end
)

echo.
echo 编译浏览器 (opcua_browser.exe)...
g++ -DUSE_OPEN62541 -std=c++11 -I"C:\vcpkg\installed\x64-windows\include" -L"C:\vcpkg\installed\x64-windows\lib" opcua_browser.cpp -lopen62541 -lws2_32 -o opcua_browser.exe

if %ERRORLEVEL% EQU 0 (
    echo ✓ opcua_browser.exe 编译成功
) else (
    echo ✗ opcua_browser.exe 编译失败
    goto end
)

echo.
echo 编译完成！
echo.
echo 使用方法：
echo   opcua_simple.exe    - 运行简化版客户端，读取配置的节点
echo   opcua_browser.exe   - 运行浏览器，查看服务器节点结构
echo.

:end
pause
