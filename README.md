# OPC UA C++ 客户端

这是一个功能完整的OPC UA客户端实现，所有服务器配置参数都设置为变量，方便您根据实际需求进行修改。

## 功能特性

- **完全配置化**: 所有连接参数、安全设置、认证信息都通过变量配置
- **安全支持**: 支持多种安全策略和认证方法
- **灵活的节点管理**: 可配置多个监控节点
- **错误处理**: 完善的错误处理和重连机制
- **日志记录**: 可配置的日志级别和输出
- **模拟模式**: 即使没有OPC UA库也可以运行（模拟模式）

## 主要配置参数

### 服务器连接配置
```cpp
std::string serverUrl = "opc.tcp://localhost:4840";  // 服务器URL
std::string serverName = "OPC UA Server";            // 服务器名称
int serverPort = 4840;                               // 服务器端口
std::string serverHost = "localhost";                // 服务器主机
```

### 安全配置
```cpp
std::string securityPolicy = "None";                 // 安全策略
std::string securityMode = "None";                   // 安全模式
std::string certificatePath = "";                    // 客户端证书路径
std::string privateKeyPath = "";                     // 客户端私钥路径
```

### 认证配置
```cpp
bool useAuthentication = false;                      // 是否使用认证
std::string username = "";                           // 用户名
std::string password = "";                           // 密码
std::string authenticationMethod = "Anonymous";      // 认证方法
```

### 节点配置
```cpp
std::vector<std::string> nodeIds = {                 // 要监控的节点ID列表
    "ns=2;i=2",
    "ns=2;i=3", 
    "ns=2;s=Temperature",
    "ns=2;s=Pressure"
};
```

## 编译和运行

### 方法1: 使用CMake（推荐）

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
cmake --build .

# 运行
./bin/opcua_client
```

### 方法2: 直接编译

```bash
# 不使用OPC UA库（模拟模式）
g++ -std=c++17 opcua.cpp -o opcua_client

# 使用open62541库
g++ -std=c++17 -DUSE_OPEN62541 opcua.cpp -lopen62541 -o opcua_client
```

## 安装OPC UA库（可选）

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install libopen62541-dev
```

### Windows (使用vcpkg)
```bash
vcpkg install open62541
```

### 从源码编译
```bash
git clone https://github.com/open62541/open62541.git
cd open62541
mkdir build && cd build
cmake ..
make -j4
sudo make install
```

## 使用示例

### 基本使用
```cpp
// 创建默认配置
OPCUAClientConfig config;

// 修改服务器地址
config.serverUrl = "opc.tcp://192.168.1.100:4840";

// 创建客户端
OPCUAClient client(config);

// 连接并读取数据
if (client.connect()) {
    client.readNode("ns=2;s=Temperature");
    client.disconnect();
}
```

### 带认证的连接
```cpp
OPCUAClientConfig config;
config.serverUrl = "opc.tcp://secure-server:4840";
config.useAuthentication = true;
config.username = "admin";
config.password = "password";
config.authenticationMethod = "UserName";

OPCUAClient client(config);
```

### 安全连接
```cpp
OPCUAClientConfig config;
config.serverUrl = "opc.tcp://secure-server:4840";
config.securityPolicy = "Basic256Sha256";
config.securityMode = "SignAndEncrypt";
config.certificatePath = "./client_cert.der";
config.privateKeyPath = "./client_key.pem";

OPCUAClient client(config);
```

## 配置参数说明

| 参数类别 | 主要参数 | 说明 |
|---------|---------|------|
| 连接配置 | serverUrl, connectionTimeout, sessionTimeout | 控制服务器连接 |
| 安全配置 | securityPolicy, securityMode, certificatePath | 控制安全通信 |
| 认证配置 | useAuthentication, username, password | 控制用户认证 |
| 节点配置 | nodeIds, samplingInterval, queueSize | 控制数据采集 |
| 日志配置 | enableLogging, logLevel, logFilePath | 控制日志输出 |
| 性能配置 | maxMessageSize, sendBufferSize | 控制性能参数 |

## 常见问题

### Q: 如何连接到不同的服务器？
A: 修改 `config.serverUrl` 参数，例如：`config.serverUrl = "opc.tcp://192.168.1.100:4840";`

### Q: 如何添加用户名密码认证？
A: 设置以下参数：
```cpp
config.useAuthentication = true;
config.username = "your_username";
config.password = "your_password";
config.authenticationMethod = "UserName";
```

### Q: 如何监控不同的节点？
A: 修改 `config.nodeIds` 向量，添加您需要的节点ID。

### Q: 程序运行但无法连接到服务器？
A: 检查服务器地址、端口是否正确，服务器是否运行，网络是否可达。

## 许可证

本项目采用MIT许可证。
